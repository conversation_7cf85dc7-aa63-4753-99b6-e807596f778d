import { Injectable } from '@angular/core';

export interface FieldPosition {
  row: number;
  column: number;
  colSize: number;
  rowSize: number;
  field: any;
  originalIndex: number;
}

export interface MatrixCell {
  field?: any;
  occupied: boolean;
  colSpan: number;
  rowSpan: number;
}

export interface PositioningResult {
  useMatrixLayout: boolean;
  matrixGrid?: MatrixCell[][];
  columns?: any[][];
  maxColumns: number;
  maxRows: number;
}

@Injectable({
  providedIn: 'root'
})
export class FieldPositioningService {

  constructor() { }

  /**
   * Main method to position fields based on metadata
   * @param fields - Array of field metadata
   * @param defaultColumnCount - Default number of columns for round-robin fallback
   * @returns PositioningResult with layout information
   */
  positionFields(fields: any[], defaultColumnCount: number = 1): PositioningResult {
    // Check if fields have positioning metadata
    const hasPositioningData = this.hasPositioningMetadata(fields);
    
    if (hasPositioningData) {
      return this.createMatrixLayout(fields, defaultColumnCount);
    } else {
      // Fallback to round-robin distribution
      return this.createRoundRobinLayout(fields, defaultColumnCount);
    }
  }

  /**
   * Check if fields contain positioning metadata
   * @param fields - Array of field metadata
   * @returns boolean indicating if positioning data is available
   */
  private hasPositioningMetadata(fields: any[]): boolean {
    return fields.some(field => 
      field.hasOwnProperty('row') && 
      field.hasOwnProperty('column') &&
      typeof field.row === 'number' && 
      typeof field.column === 'number'
    );
  }

  /**
   * Create matrix-based layout using positioning metadata
   * @param fields - Array of field metadata
   * @param defaultColumnCount - Default column count
   * @returns PositioningResult with matrix layout
   */
  private createMatrixLayout(fields: any[], defaultColumnCount: number): PositioningResult {
    // Prepare field positions with defaults
    const fieldPositions: FieldPosition[] = fields.map((field, index) => ({
      row: field.row || 1,
      column: field.column || 1,
      colSize: field.colSize || 1,
      rowSize: field.rowSize || 1,
      field: field,
      originalIndex: index
    }));

    // Calculate matrix dimensions
    const maxColumns = Math.max(
      defaultColumnCount,
      ...fieldPositions.map(fp => fp.column + fp.colSize - 1)
    );
    const maxRows = Math.max(
      1,
      ...fieldPositions.map(fp => fp.row + fp.rowSize - 1)
    );

    // Create matrix grid
    const matrixGrid: MatrixCell[][] = Array(maxRows).fill(null).map(() =>
      Array(maxColumns).fill(null).map(() => ({
        occupied: false,
        colSpan: 1,
        rowSpan: 1
      }))
    );

    // Sort fields by row, then column, then original index for conflict resolution
    fieldPositions.sort((a, b) => {
      if (a.row !== b.row) return a.row - b.row;
      if (a.column !== b.column) return a.column - b.column;
      return a.originalIndex - b.originalIndex;
    });

    // Place fields in matrix
    fieldPositions.forEach(fieldPos => {
      this.placeFieldInMatrix(matrixGrid, fieldPos, maxRows, maxColumns);
    });

    return {
      useMatrixLayout: true,
      matrixGrid,
      maxColumns,
      maxRows
    };
  }

  /**
   * Place a field in the matrix grid
   * @param matrix - The matrix grid
   * @param fieldPos - Field position information
   * @param maxRows - Maximum number of rows
   * @param maxColumns - Maximum number of columns
   */
  private placeFieldInMatrix(
    matrix: MatrixCell[][], 
    fieldPos: FieldPosition, 
    maxRows: number, 
    maxColumns: number
  ): void {
    const startRow = Math.max(0, fieldPos.row - 1); // Convert to 0-based index
    const startCol = Math.max(0, fieldPos.column - 1); // Convert to 0-based index
    const endRow = Math.min(maxRows - 1, startRow + fieldPos.rowSize - 1);
    const endCol = Math.min(maxColumns - 1, startCol + fieldPos.colSize - 1);

    // Check if the position is available
    let canPlace = true;
    for (let r = startRow; r <= endRow; r++) {
      for (let c = startCol; c <= endCol; c++) {
        if (matrix[r][c].occupied) {
          canPlace = false;
          break;
        }
      }
      if (!canPlace) break;
    }

    if (canPlace) {
      // Place the field
      for (let r = startRow; r <= endRow; r++) {
        for (let c = startCol; c <= endCol; c++) {
          matrix[r][c].occupied = true;
          if (r === startRow && c === startCol) {
            // Main cell contains the field
            matrix[r][c].field = fieldPos.field;
            matrix[r][c].colSpan = fieldPos.colSize;
            matrix[r][c].rowSpan = fieldPos.rowSize;
          }
        }
      }
    } else {
      // Find next available position
      this.findAndPlaceInNextAvailablePosition(matrix, fieldPos, maxRows, maxColumns);
    }
  }

  /**
   * Find next available position for a field when preferred position is occupied
   * @param matrix - The matrix grid
   * @param fieldPos - Field position information
   * @param maxRows - Maximum number of rows
   * @param maxColumns - Maximum number of columns
   */
  private findAndPlaceInNextAvailablePosition(
    matrix: MatrixCell[][], 
    fieldPos: FieldPosition, 
    maxRows: number, 
    maxColumns: number
  ): void {
    for (let r = 0; r < maxRows; r++) {
      for (let c = 0; c < maxColumns; c++) {
        if (!matrix[r][c].occupied) {
          const endRow = Math.min(maxRows - 1, r + fieldPos.rowSize - 1);
          const endCol = Math.min(maxColumns - 1, c + fieldPos.colSize - 1);
          
          // Check if we can place the field here
          let canPlace = true;
          for (let checkR = r; checkR <= endRow; checkR++) {
            for (let checkC = c; checkC <= endCol; checkC++) {
              if (matrix[checkR][checkC].occupied) {
                canPlace = false;
                break;
              }
            }
            if (!canPlace) break;
          }
          
          if (canPlace) {
            // Place the field
            for (let placeR = r; placeR <= endRow; placeR++) {
              for (let placeC = c; placeC <= endCol; placeC++) {
                matrix[placeR][placeC].occupied = true;
                if (placeR === r && placeC === c) {
                  matrix[placeR][placeC].field = fieldPos.field;
                  matrix[placeR][placeC].colSpan = fieldPos.colSize;
                  matrix[placeR][placeC].rowSpan = fieldPos.rowSize;
                }
              }
            }
            return;
          }
        }
      }
    }
  }

  /**
   * Create round-robin layout (fallback)
   * @param fields - Array of field metadata
   * @param columnCount - Number of columns
   * @returns PositioningResult with round-robin layout
   */
  private createRoundRobinLayout(fields: any[], columnCount: number): PositioningResult {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    fields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });

    return {
      useMatrixLayout: false,
      columns,
      maxColumns: columnCount,
      maxRows: Math.ceil(fields.length / columnCount)
    };
  }

  /**
   * Convert matrix grid to column-based layout for template compatibility
   * @param matrixGrid - The matrix grid
   * @param maxColumns - Maximum number of columns
   * @returns Array of field columns
   */
  convertMatrixToColumns(matrixGrid: MatrixCell[][], maxColumns: number): any[][] {
    const columns: any[][] = Array.from({ length: maxColumns }, () => []);

    matrixGrid.forEach(row => {
      row.forEach((cell, colIndex) => {
        if (cell.field && !cell.field._addedToColumn) {
          columns[colIndex].push({
            ...cell.field,
            _matrixColSpan: cell.colSpan,
            _matrixRowSpan: cell.rowSpan
          });
          cell.field._addedToColumn = true;
        }
      });
    });

    // Clean up temporary flags
    matrixGrid.forEach(row => {
      row.forEach(cell => {
        if (cell.field && cell.field._addedToColumn) {
          delete cell.field._addedToColumn;
        }
      });
    });

    return columns;
  }

  /**
   * Get fields arranged in row-major order from matrix
   * @param matrixGrid - The matrix grid
   * @returns Array of fields in row-major order
   */
  getFieldsInRowMajorOrder(matrixGrid: MatrixCell[][]): any[] {
    const fields: any[] = [];
    const addedFields = new Set();

    matrixGrid.forEach(row => {
      row.forEach(cell => {
        if (cell.field && !addedFields.has(cell.field.fieldName)) {
          fields.push({
            ...cell.field,
            _matrixColSpan: cell.colSpan,
            _matrixRowSpan: cell.rowSpan
          });
          addedFields.add(cell.field.fieldName);
        }
      });
    });

    return fields;
  }

  /**
   * Get matrix layout information for CSS Grid
   * @param matrixGrid - The matrix grid
   * @returns Object with CSS Grid layout information
   */
  getMatrixLayoutInfo(matrixGrid: MatrixCell[][]): {
    gridTemplateRows: string;
    gridTemplateColumns: string;
    fieldPositions: Map<string, { gridRow: string; gridColumn: string }>;
  } {
    const rows = matrixGrid.length;
    const cols = matrixGrid[0]?.length || 0;

    const gridTemplateRows = `repeat(${rows}, 1fr)`;
    const gridTemplateColumns = `repeat(${cols}, 1fr)`;

    const fieldPositions = new Map<string, { gridRow: string; gridColumn: string }>();

    matrixGrid.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        if (cell.field) {
          const gridRow = cell.rowSpan > 1
            ? `${rowIndex + 1} / span ${cell.rowSpan}`
            : `${rowIndex + 1}`;
          const gridColumn = cell.colSpan > 1
            ? `${colIndex + 1} / span ${cell.colSpan}`
            : `${colIndex + 1}`;

          fieldPositions.set(cell.field.fieldName, {
            gridRow,
            gridColumn
          });
        }
      });
    });

    return {
      gridTemplateRows,
      gridTemplateColumns,
      fieldPositions
    };
  }

  /**
   * Validate field positioning metadata
   * @param fields - Array of field metadata
   * @returns Array of validation errors
   */
  validatePositioning(fields: any[]): string[] {
    const errors: string[] = [];

    fields.forEach((field, index) => {
      if (field.row !== undefined && (typeof field.row !== 'number' || field.row < 1)) {
        errors.push(`Field ${field.fieldName || index}: row must be a positive number`);
      }

      if (field.column !== undefined && (typeof field.column !== 'number' || field.column < 1)) {
        errors.push(`Field ${field.fieldName || index}: column must be a positive number`);
      }

      if (field.colSize !== undefined && (typeof field.colSize !== 'number' || field.colSize < 1)) {
        errors.push(`Field ${field.fieldName || index}: colSize must be a positive number`);
      }

      if (field.rowSize !== undefined && (typeof field.rowSize !== 'number' || field.rowSize < 1)) {
        errors.push(`Field ${field.fieldName || index}: rowSize must be a positive number`);
      }
    });

    return errors;
  }
}
